"""
语音识别相关数据模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from .common_models import BaseResponse


class AudioFileInfo(BaseModel):
    """音频文件信息"""
    original_name: Optional[str] = Field(default=None, description="原始文件名")
    size: int = Field(..., description="文件大小（字节）")
    format: str = Field(..., description="文件格式")
    duration: Optional[float] = Field(default=None, description="音频时长（秒）")
    sample_rate: Optional[int] = Field(default=None, description="采样率")
    channels: Optional[int] = Field(default=None, description="声道数")


class SpeechRecognitionData(BaseModel):
    """语音识别数据"""
    text: str = Field(..., description="识别的文本内容")
    confidence: float = Field(..., ge=0.0, le=1.0, description="识别置信度")
    duration: float = Field(..., ge=0.0, description="音频时长（秒）")
    language: str = Field(..., description="识别语言")
    model: str = Field(..., description="使用的模型")
    file_info: Optional[AudioFileInfo] = Field(default=None, description="文件信息")


class SpeechRecognitionResponse(BaseResponse):
    """语音识别API响应"""
    data: Optional[SpeechRecognitionData] = Field(default=None, description="识别数据")


class ServiceStatusData(BaseModel):
    """服务状态数据"""
    service: str = Field(..., description="服务名称")
    status: str = Field(..., description="服务状态")
    model: str = Field(..., description="当前使用的模型")
    supported_formats: List[str] = Field(..., description="支持的格式")
    max_file_size: int = Field(..., description="最大文件大小")
    version: str = Field(..., description="版本号")


class ServiceStatusResponse(BaseResponse):
    """服务状态响应"""
    data: Optional[ServiceStatusData] = Field(default=None, description="状态数据")


class ModelInfo(BaseModel):
    """模型信息"""
    name: str = Field(..., description="模型名称")
    description: str = Field(..., description="模型描述")
    type: str = Field(..., description="模型类型")
    current: bool = Field(..., description="是否为当前使用的模型")


class ModelsData(BaseModel):
    """模型列表数据"""
    current_model: str = Field(..., description="当前模型")
    models: List[ModelInfo] = Field(..., description="模型列表")
    supported_formats: List[str] = Field(..., description="支持的格式")


class ModelsResponse(BaseResponse):
    """模型列表响应"""
    data: Optional[ModelsData] = Field(default=None, description="模型数据")

"""
日志工具模块
"""

import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional

from ..config import get_settings


def get_daily_log_file(base_log_file: str) -> str:
    """
    生成当天的日志文件路径

    Args:
        base_log_file: 基础日志文件路径，如 "./logs/ai_service.log"

    Returns:
        当天的日志文件路径，如 "./logs/20250731.log"
    """
    log_dir = os.path.dirname(base_log_file)
    today = datetime.now().strftime("%Y%m%d")
    return os.path.join(log_dir, f"{today}.log")


def cleanup_old_logs(base_log_file: str, keep_days: int):
    """
    清理超过指定天数的日志文件

    Args:
        base_log_file: 基础日志文件路径
        keep_days: 保留的天数
    """
    try:
        log_dir = os.path.dirname(base_log_file)
        if not os.path.exists(log_dir):
            return

        from datetime import timedelta
        cutoff_date = datetime.now() - timedelta(days=keep_days)

        # 遍历日志目录中的文件
        for filename in os.listdir(log_dir):
            if filename.endswith('.log') and len(filename) == 12:  # yyyyMMdd.log格式
                try:
                    # 从文件名提取日期
                    date_str = filename[:8]  # 取前8位：yyyyMMdd
                    file_date = datetime.strptime(date_str, "%Y%m%d")

                    # 如果文件日期早于截止日期，删除文件
                    if file_date < cutoff_date:
                        file_path = os.path.join(log_dir, filename)
                        os.remove(file_path)
                        print(f"已清理旧日志文件: {filename}")

                except (ValueError, OSError) as e:
                    # 忽略无法解析日期或删除失败的文件
                    continue

    except Exception as e:
        print(f"清理日志文件时出错: {e}")


def setup_logger(
    name: Optional[str] = None,
    level: Optional[str] = None,
    log_file: Optional[str] = None
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        
    Returns:
        配置好的日志记录器
    """
    settings = get_settings()
    
    # 使用参数或配置中的值
    logger_name = name or "ai_server"
    log_level = level or settings.log_level
    log_file_path = log_file or settings.log_file
    
    # 创建日志记录器
    logger = logging.getLogger(logger_name)
    
    # 如果已经配置过，直接返回
    if logger.handlers:
        return logger
    
    # 设置日志级别
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # 创建格式化器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（按日期命名）
    if log_file_path:
        try:
            # 生成当天的日志文件路径
            daily_log_file = get_daily_log_file(log_file_path)

            # 确保日志目录存在
            log_dir = os.path.dirname(daily_log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            # 创建文件处理器，使用当天日期命名
            # 文件名格式：20250731.log
            file_handler = logging.FileHandler(
                filename=daily_log_file,
                encoding='utf-8',
                mode='a'  # 追加模式
            )
            file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            # 清理旧的日志文件
            cleanup_old_logs(log_file_path, settings.log_backup_count)

        except Exception as e:
            logger.error(f"创建文件日志处理器失败: {e}")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    return logging.getLogger(name)


class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志记录器"""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)

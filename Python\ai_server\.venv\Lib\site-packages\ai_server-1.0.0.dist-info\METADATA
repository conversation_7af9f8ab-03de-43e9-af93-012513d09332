Metadata-Version: 2.4
Name: ai-server
Version: 1.0.0
Summary: AI服务集成平台，支持语音识别等多种AI功能
Project-URL: Homepage, https://github.com/your-org/ai-server
Project-URL: Documentation, https://github.com/your-org/ai-server/docs
Project-URL: Repository, https://github.com/your-org/ai-server.git
Project-URL: Issues, https://github.com/your-org/ai-server/issues
Author-email: CRM Team <<EMAIL>>
License: MIT
Keywords: ai,fastapi,recognition,speech
Classifier: Development Status :: 4 - Beta
Classifier: Framework :: FastAPI
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8.1
Requires-Dist: aiofiles>=23.2.0
Requires-Dist: dashscope>=1.14.0
Requires-Dist: fastapi>=0.104.0
Requires-Dist: ffmpeg-python>=0.2.0
Requires-Dist: librosa>=0.10.1
Requires-Dist: numpy>=1.24.0
Requires-Dist: psutil>=5.9.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydub>=0.25.1
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: pyyaml>=6.0
Requires-Dist: soundfile>=0.12.1
Requires-Dist: uvicorn[standard]>=0.24.0
Provides-Extra: dev
Requires-Dist: black>=23.0.0; extra == 'dev'
Requires-Dist: flake8>=6.0.0; extra == 'dev'
Requires-Dist: httpx>=0.25.0; extra == 'dev'
Requires-Dist: isort>=5.12.0; extra == 'dev'
Requires-Dist: mypy>=1.7.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'dev'
Requires-Dist: pytest>=7.4.0; extra == 'dev'
Provides-Extra: test
Requires-Dist: httpx>=0.25.0; extra == 'test'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'test'
Requires-Dist: pytest-cov>=4.1.0; extra == 'test'
Requires-Dist: pytest>=7.4.0; extra == 'test'
Description-Content-Type: text/markdown

# AI服务平台

基于FastAPI的AI服务集成平台，支持语音识别等多种AI功能，采用模块化架构设计。

## 🌟 功能特性

- 🎤 **语音识别服务** - 基于阿里百炼Paraformer模型，支持多种音频格式
- 🏗️ **模块化架构** - 清晰的分层设计，易于扩展和维护
- 🔧 **音频格式转换** - 智能音频预处理，支持WebM等多种格式
- 🚀 **高性能API** - 基于FastAPI，支持异步处理和自动文档生成
- 📊 **健康监控** - 完整的健康检查和系统监控功能
- 🛡️ **安全可靠** - CORS支持、请求验证、错误处理
- 📝 **完整日志** - 结构化日志记录，支持日志轮转

## 🏗️ 项目结构

```text
ai_server/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config/            # 配置管理
│   │   ├── __init__.py
│   │   └── settings.py    # 配置类
│   ├── services/          # 业务服务
│   │   ├── __init__.py
│   │   └── speech_service.py  # 语音识别服务
│   ├── utils/             # 辅助工具
│   │   ├── __init__.py
│   │   ├── audio_converter.py  # 音频格式转换
│   │   ├── file_handler.py     # 文件处理
│   │   └── logger.py           # 日志工具
│   └── api/              # API控制器
│       ├── __init__.py
│       ├── speech_controller.py    # 语音识别控制器
│       └── health_controller.py    # 健康检查控制器
├── pyproject.toml         # Python依赖配置
├── config.yaml           # 应用配置文件
├── .env.example          # 环境变量示例
└── README.md             # 说明文档
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- FFmpeg（用于音频格式转换）

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd ai_server

# 安装依赖
pip install -e .

# 或使用开发模式
pip install -e ".[dev]"
```

### 3. 配置环境

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 4. 必需配置

```env
# 阿里百炼API密钥（必需）
DASHSCOPE_API_KEY=your_api_key_here

# FFmpeg路径（用于WebM等格式支持）
FFMPEG_PATH=D:\ffmpeg\bin\ffmpeg.exe
FFPROBE_PATH=D:\ffmpeg\bin\ffprobe.exe
```

### 5. 启动服务

```bash
# 直接启动
python -m app.main

# 或使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 6. 访问服务

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health
- **语音识别**: POST http://localhost:8000/api/v1/speech/recognize

## 📚 API接口

### 语音识别

**POST** `/api/v1/speech/recognize`

上传音频文件进行语音识别。

**请求参数：**
- `audio`: 音频文件（multipart/form-data）

**响应示例：**
```json
{
  "succeed": true,
  "data": {
    "text": "这是识别出的文本内容",
    "confidence": 0.95,
    "duration": 3.2,
    "language": "zh",
    "model": "paraformer-realtime-v1",
    "file_info": {
      "original_name": "recording.webm",
      "size": 12345,
      "format": "webm"
    }
  },
  "messages": [],
  "timestamp": null
}
```

### 服务状态

**GET** `/api/v1/speech/status`

获取语音识别服务状态。

### 支持的模型

**GET** `/api/v1/speech/models`

获取支持的语音识别模型列表。

### 健康检查

**GET** `/api/v1/health/`

基础健康检查。

**GET** `/api/v1/health/detailed`

详细健康检查，包含系统资源、配置状态等信息。

## 🔧 配置说明

### 环境变量配置

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `DASHSCOPE_API_KEY` | 阿里百炼API密钥 | - | ✅ |
| `DASHSCOPE_MODEL` | 语音识别模型 | `paraformer-realtime-v1` | ❌ |
| `FFMPEG_PATH` | FFmpeg可执行文件路径 | - | ❌ |
| `FFPROBE_PATH` | FFprobe可执行文件路径 | - | ❌ |
| `HOST` | 服务监听地址 | `0.0.0.0` | ❌ |
| `PORT` | 服务端口 | `8000` | ❌ |
| `DEBUG` | 调试模式 | `True` | ❌ |

### 支持的模型

| 模型名称 | 描述 | 适用场景 |
|---------|------|----------|
| `paraformer-realtime-v1` | 实时模型（推荐） | 实时语音识别，响应速度快 |
| `paraformer-v1` | 标准模型 | 高精度语音识别 |
| `paraformer-8k-v1` | 8k采样率模型 | 电话语音等低采样率场景 |
| `paraformer-mtl-v1` | 多任务学习模型 | 支持多种语音任务 |

### 支持的音频格式

- WAV, MP3, M4A, FLAC, AAC
- OGG, WEBM, AMR, OPUS, WMA

## 🛠️ 开发指南

### 添加新的AI服务

1. 在 `app/services/` 目录下创建新的服务类
2. 在 `app/api/` 目录下创建对应的控制器
3. 在 `app/main.py` 中注册新的路由

### 代码规范

```bash
# 格式化代码
black .
isort .

# 检查代码质量
flake8
mypy .

# 运行测试
pytest
```

## 🐳 Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY pyproject.toml .
RUN pip install -e .

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p uploads temp logs

# 暴露端口
EXPOSE 8000

# 启动服务
CMD ["python", "-m", "app.main"]
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `DASHSCOPE_API_KEY` 是否正确配置
   - 确认API密钥有效且有足够配额

2. **FFmpeg相关错误**
   - 确认FFmpeg已正确安装
   - 检查 `FFMPEG_PATH` 和 `FFPROBE_PATH` 配置
   - 验证文件权限

3. **音频格式不支持**
   - 检查文件格式是否在支持列表中
   - 确认FFmpeg配置正确（用于WebM等格式）

4. **文件上传失败**
   - 检查文件大小是否超过限制
   - 确认上传目录权限正确

### 日志查看

```bash
# 查看实时日志
tail -f logs/ai_service.log

# 查看错误日志
grep ERROR logs/ai_service.log
```

## 📈 性能优化

- 使用异步处理提高并发性能
- 实现音频文件缓存机制
- 配置负载均衡和反向代理
- 监控系统资源使用情况

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

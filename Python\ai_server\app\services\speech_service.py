"""
语音识别服务
"""

import os
import asyncio
import librosa
import soundfile as sf
from typing import Dict, Any, Optional
from pathlib import Path
from fastapi import UploadFile

import dashscope
from dashscope.audio.asr import Recognition

from ..config import get_settings
from ..utils import AudioConverter, FileHandler, LoggerMixin
from ..models import AudioFileInfo


class SpeechService(LoggerMixin):
    """语音识别服务"""
    
    def __init__(self):
        self.settings = get_settings()
        self.audio_converter = AudioConverter()
        self.file_handler = FileHandler()
        self._initialized = False
        self._initialization_lock = asyncio.Lock() if hasattr(asyncio, '_get_running_loop') and asyncio._get_running_loop() else None
    
    async def _ensure_initialized(self):
        """确保服务已初始化"""
        if self._initialized:
            return

        # 创建锁（如果还没有）
        if self._initialization_lock is None:
            self._initialization_lock = asyncio.Lock()

        async with self._initialization_lock:
            # 双重检查，防止重复初始化
            if not self._initialized:
                await self.initialize()

    async def initialize(self):
        """初始化服务"""
        try:
            # 设置阿里百炼API密钥
            dashscope.api_key = self.settings.dashscope_api_key

            # 测试API连接
            await self._test_api_connection()

            self._initialized = True
            self.logger.info(f"语音识别服务初始化成功，使用模型: {self.settings.dashscope_model}")

        except Exception as e:
            self.logger.error(f"语音识别服务初始化失败: {e}")
            raise
    
    async def _test_api_connection(self):
        """测试API连接"""
        try:
            # 这里可以添加API连接测试逻辑
            self.logger.info("API连接测试通过")
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            raise
    
    async def recognize_speech(self, upload_file: UploadFile) -> Dict[str, Any]:
        """
        语音识别主方法

        Args:
            upload_file: 上传的音频文件

        Returns:
            识别结果字典
        """
        # 确保服务已初始化
        await self._ensure_initialized()
        
        temp_file_path = None
        processed_file_path = None
        
        try:
            # 获取音频文件信息
            audio_info = await self._get_audio_info(upload_file)
            self.logger.info(f"处理音频文件: {audio_info.original_name}, 大小: {audio_info.size}")

            # 保存上传的文件到临时目录
            file_extension = self.file_handler.get_file_extension(upload_file.filename or "unknown")
            temp_file_path = self.file_handler.get_temp_file_path(f".{file_extension}")
            await self.file_handler.save_upload_file_async(upload_file, temp_file_path)

            # 验证文件格式
            if not self.audio_converter.is_supported_format(temp_file_path):
                raise ValueError(f"不支持的音频格式: {file_extension}")

            # 预处理音频文件
            processed_file_path = await self._preprocess_audio(temp_file_path, audio_info)

            # 调用阿里百炼API进行语音识别
            recognition_result = await self._call_dashscope_api(processed_file_path)

            # 获取音频时长
            duration = await self._get_audio_duration(processed_file_path)

            # 构建返回结果
            result = {
                "success": True,
                "text": recognition_result.get('text', ''),
                "confidence": recognition_result.get('confidence', 0.0),
                "duration": duration,
                "language": self.settings.default_language,
                "model": self.settings.dashscope_model,
                "file_info": {
                    "original_name": upload_file.filename,
                    "size": audio_info.size,
                    "format": file_extension,
                }
            }

            return result
            
        except Exception as e:
            self.logger.error(f"语音识别失败: {e}")
            raise
        finally:
            # 清理临时文件
            if temp_file_path:
                self.file_handler.cleanup_file(temp_file_path)
            if processed_file_path and processed_file_path != temp_file_path:
                self.file_handler.cleanup_file(processed_file_path)

    async def _get_audio_info(self, audio_file: UploadFile) -> AudioFileInfo:
        """获取音频文件信息"""
        return AudioFileInfo(
            original_name=audio_file.filename or "unknown",
            size=audio_file.size or 0,
            format=self.file_handler.get_file_extension(audio_file.filename or "unknown")
        )

    async def _preprocess_audio(self, file_path: str, audio_info: AudioFileInfo) -> str:
        """
        预处理音频文件
        - 转换格式为WAV
        - 调整采样率
        - 单声道转换

        Args:
            file_path: 原始音频文件路径
            audio_info: 音频文件信息

        Returns:
            处理后的音频文件路径
        """
        try:
            self.logger.info(f"开始预处理音频文件: {file_path}")

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError("音频文件为空")
            elif file_size < 1000:  # 小于1KB
                raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏")

            # 获取音频信息
            audio_details = self.audio_converter.get_audio_info(file_path)
            self.logger.info(f"音频信息: 时长={audio_details['duration']:.2f}秒, "
                           f"采样率={audio_details['frame_rate']}Hz, "
                           f"声道数={audio_details['channels']}")

            # 转换为WAV格式（16kHz, 单声道）
            wav_path = self.file_handler.create_temp_file(suffix='.wav', prefix='processed_')
            processed_path = self.audio_converter.convert_to_wav(file_path, wav_path)

            # 验证处理后的文件
            processed_size = os.path.getsize(processed_path)
            self.logger.info(f"音频预处理完成: {processed_path}, 大小: {processed_size} bytes")

            return processed_path

        except Exception as e:
            self.logger.error(f"音频预处理失败: {e}")
            raise
    
    async def _call_dashscope_api(self, file_path: str) -> Dict[str, Any]:
        """
        执行语音识别
        
        Args:
            file_path: 预处理后的音频文件路径
            
        Returns:
            识别结果
        """
        try:
            self.logger.info(f"开始语音识别: {file_path}")
            
            # 使用阿里百炼的语音识别API
            recognition = Recognition(
                model=self.settings.dashscope_model,
                format='wav',
                sample_rate=16000,
                callback=None # type: ignore
            )
            
            self.logger.info(f"使用百炼模型: {self.settings.dashscope_model}")
            
            # 执行识别
            result = recognition.call(file_path)
            self.logger.info(f"API调用完成，状态码: {result.status_code}")
            
            if result.status_code == 200:
                # 解析识别结果
                return self._parse_recognition_result(result)
            else:
                error_msg = f"API调用失败，状态码: {result.status_code}"
                if hasattr(result, 'message'):
                    error_msg += f", 错误信息: {result.message}"
                raise RuntimeError(error_msg)
                
        except Exception as e:
            self.logger.error(f"语音识别执行失败: {e}")
            raise
    
    def _parse_recognition_result(self, result) -> Dict[str, Any]:
        """
        解析识别结果
        
        Args:
            result: API返回结果
            
        Returns:
            解析后的结果字典
        """
        try:
            # 获取识别文本
            text = ""
            confidence = 0.0
            duration = 0.0
            
            if hasattr(result, 'output') and result.output:
                output = result.output
                self.logger.info(f"API返回的output结构: {output}")
                
                # 提取文本
                if isinstance(output, dict):
                    if 'sentence' in output:
                        sentence_data = output['sentence']
                        if isinstance(sentence_data, dict) and 'text' in sentence_data:
                            text = sentence_data['text']
                        elif isinstance(sentence_data, str):
                            text = sentence_data
                    elif 'text' in output:
                        text = output['text']
                    
                    # 提取置信度和时长
                    confidence = output.get('confidence', 0.0)
                    duration = output.get('duration', 0.0)
                
                elif isinstance(output, str):
                    text = output
            
            # 如果没有获取到文本，记录警告
            if not text:
                self.logger.warning(f"未能提取识别文本，output内容: {result.output}")
            
            # 构建返回结果
            recognition_result = {
                "success": True,
                "text": text.strip() if text else "",
                "confidence": confidence,
                "duration": duration,
                "language": self.settings.default_language,
                "model": self.settings.dashscope_model,
                "error": ""
            }
            
            self.logger.info(f"识别结果: 文本='{recognition_result['text']}', "
                           f"置信度={recognition_result['confidence']}, "
                           f"时长={recognition_result['duration']}秒")
            
            return recognition_result
            
        except Exception as e:
            self.logger.error(f"解析识别结果失败: {e}")
            return {
                "success": False,
                "text": "",
                "confidence": 0.0,
                "duration": 0.0,
                "language": self.settings.default_language,
                "model": self.settings.dashscope_model,
                "error": f"结果解析失败: {str(e)}"
            }

    async def _get_audio_duration(self, file_path: str) -> float:
        """获取音频时长"""
        try:
            audio_info = self.audio_converter.get_audio_info(file_path)
            return audio_info.get('duration', 0.0)
        except Exception as e:
            self.logger.warning(f"获取音频时长失败: {e}")
            return 0.0

    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        # 尝试初始化（如果还没有初始化）
        try:
            await self._ensure_initialized()
            status = "healthy"
        except Exception as e:
            self.logger.error(f"服务状态检查失败: {e}")
            status = "error"

        return {
            "service": "speech_recognition",
            "status": status,
            "model": self.settings.dashscope_model,
            "supported_formats": self.settings.supported_formats,
            "max_file_size": self.settings.max_file_size,
            "version": "1.0.0"
        }

"""
语音识别服务
"""

import os
import asyncio
from typing import Dict, Any, Optional
from fastapi import UploadFile

import dashscope
from dashscope.audio.asr import Recognition

from ..config import get_settings
from ..utils import AudioConverter, FileHandler, LoggerMixin


class SpeechService(LoggerMixin):
    """语音识别服务"""
    
    def __init__(self):
        self.settings = get_settings()
        self.audio_converter = AudioConverter()
        self.file_handler = FileHandler()
        self._initialized = False
        
        # 初始化服务
        asyncio.create_task(self.initialize())
    
    async def initialize(self):
        """初始化服务"""
        try:
            # 设置阿里百炼API密钥
            dashscope.api_key = self.settings.dashscope_api_key
            
            # 测试API连接
            await self._test_api_connection()
            
            self._initialized = True
            self.logger.info(f"语音识别服务初始化成功，使用模型: {self.settings.dashscope_model}")
            
        except Exception as e:
            self.logger.error(f"语音识别服务初始化失败: {e}")
            raise
    
    async def _test_api_connection(self):
        """测试API连接"""
        try:
            # 这里可以添加API连接测试逻辑
            self.logger.info("API连接测试通过")
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            raise
    
    async def recognize_speech(self, upload_file: UploadFile) -> Dict[str, Any]:
        """
        语音识别主方法
        
        Args:
            upload_file: 上传的音频文件
            
        Returns:
            识别结果字典
        """
        if not self._initialized:
            raise RuntimeError("语音识别服务未初始化")
        
        temp_file_path = None
        processed_file_path = None
        
        try:
            # 保存上传文件
            temp_file_path = self.file_handler.save_upload_file(upload_file)
            self.logger.info(f"接收音频文件: {temp_file_path}")
            
            # 获取文件信息
            file_info = self.file_handler.get_file_info(temp_file_path)
            self.logger.info(f"文件信息: {file_info}")
            
            # 验证文件格式
            if not self.audio_converter.is_supported_format(temp_file_path):
                raise ValueError(f"不支持的音频格式: {file_info.get('extension', 'unknown')}")
            
            # 预处理音频文件
            processed_file_path = await self._preprocess_audio(temp_file_path)
            
            # 执行语音识别
            result = await self._perform_recognition(processed_file_path)
            
            # 添加文件信息到结果
            result.update({
                "file_info": {
                    "original_name": upload_file.filename,
                    "size": file_info.get("size", 0),
                    "format": file_info.get("extension", "").lstrip('.'),
                }
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"语音识别失败: {e}")
            raise
        finally:
            # 清理临时文件
            if temp_file_path:
                self.file_handler.cleanup_file(temp_file_path)
            if processed_file_path and processed_file_path != temp_file_path:
                self.file_handler.cleanup_file(processed_file_path)
    
    async def _preprocess_audio(self, file_path: str) -> str:
        """
        预处理音频文件
        
        Args:
            file_path: 原始音频文件路径
            
        Returns:
            处理后的音频文件路径
        """
        try:
            self.logger.info(f"开始预处理音频文件: {file_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError("音频文件为空")
            elif file_size < 1000:  # 小于1KB
                raise ValueError(f"音频文件太小({file_size} bytes)，可能录音失败或文件损坏")
            
            # 获取音频信息
            audio_info = self.audio_converter.get_audio_info(file_path)
            self.logger.info(f"音频信息: 时长={audio_info['duration']:.2f}秒, "
                           f"采样率={audio_info['frame_rate']}Hz, "
                           f"声道数={audio_info['channels']}")
            
            # 转换为WAV格式（16kHz, 单声道）
            wav_path = self.file_handler.create_temp_file(suffix='.wav', prefix='processed_')
            processed_path = self.audio_converter.convert_to_wav(file_path, wav_path)
            
            # 验证处理后的文件
            processed_size = os.path.getsize(processed_path)
            self.logger.info(f"音频预处理完成: {processed_path}, 大小: {processed_size} bytes")
            
            return processed_path
            
        except Exception as e:
            self.logger.error(f"音频预处理失败: {e}")
            raise
    
    async def _perform_recognition(self, file_path: str) -> Dict[str, Any]:
        """
        执行语音识别
        
        Args:
            file_path: 预处理后的音频文件路径
            
        Returns:
            识别结果
        """
        try:
            self.logger.info(f"开始语音识别: {file_path}")
            
            # 使用阿里百炼的语音识别API
            recognition = Recognition(
                model=self.settings.dashscope_model,
                format='wav',
                sample_rate=16000,
                callback=None # type: ignore
            )
            
            self.logger.info(f"使用百炼模型: {self.settings.dashscope_model}")
            
            # 执行识别
            result = recognition.call(file_path)
            self.logger.info(f"API调用完成，状态码: {result.status_code}")
            
            if result.status_code == 200:
                # 解析识别结果
                return self._parse_recognition_result(result)
            else:
                error_msg = f"API调用失败，状态码: {result.status_code}"
                if hasattr(result, 'message'):
                    error_msg += f", 错误信息: {result.message}"
                raise RuntimeError(error_msg)
                
        except Exception as e:
            self.logger.error(f"语音识别执行失败: {e}")
            raise
    
    def _parse_recognition_result(self, result) -> Dict[str, Any]:
        """
        解析识别结果
        
        Args:
            result: API返回结果
            
        Returns:
            解析后的结果字典
        """
        try:
            # 获取识别文本
            text = ""
            confidence = 0.0
            duration = 0.0
            
            if hasattr(result, 'output') and result.output:
                output = result.output
                self.logger.info(f"API返回的output结构: {output}")
                
                # 提取文本
                if isinstance(output, dict):
                    if 'sentence' in output:
                        sentence_data = output['sentence']
                        if isinstance(sentence_data, dict) and 'text' in sentence_data:
                            text = sentence_data['text']
                        elif isinstance(sentence_data, str):
                            text = sentence_data
                    elif 'text' in output:
                        text = output['text']
                    
                    # 提取置信度和时长
                    confidence = output.get('confidence', 0.0)
                    duration = output.get('duration', 0.0)
                
                elif isinstance(output, str):
                    text = output
            
            # 如果没有获取到文本，记录警告
            if not text:
                self.logger.warning(f"未能提取识别文本，output内容: {result.output}")
            
            # 构建返回结果
            recognition_result = {
                "success": True,
                "text": text.strip() if text else "",
                "confidence": confidence,
                "duration": duration,
                "language": self.settings.default_language,
                "model": self.settings.dashscope_model,
                "error": ""
            }
            
            self.logger.info(f"识别结果: 文本='{recognition_result['text']}', "
                           f"置信度={recognition_result['confidence']}, "
                           f"时长={recognition_result['duration']}秒")
            
            return recognition_result
            
        except Exception as e:
            self.logger.error(f"解析识别结果失败: {e}")
            return {
                "success": False,
                "text": "",
                "confidence": 0.0,
                "duration": 0.0,
                "language": self.settings.default_language,
                "model": self.settings.dashscope_model,
                "error": f"结果解析失败: {str(e)}"
            }
    
    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service": "speech_recognition",
            "status": "healthy" if self._initialized else "initializing",
            "model": self.settings.dashscope_model,
            "supported_formats": self.settings.supported_formats,
            "max_file_size": self.settings.max_file_size,
            "version": "1.0.0"
        }

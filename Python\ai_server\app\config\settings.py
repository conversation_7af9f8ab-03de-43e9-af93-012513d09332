"""
应用配置管理
"""

import os
from typing import List, Optional
from functools import lru_cache
from pathlib import Path
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = Field("AI服务平台", env="APP_NAME") # type: ignore
    app_version: str = Field("1.0.0", env="APP_VERSION") # type: ignore
    debug: bool = Field(True, env="DEBUG") # type: ignore

    # 服务配置
    host: str = Field("0.0.0.0", env="HOST") # type: ignore
    port: int = Field(8000, env="PORT") # type: ignore

    # 阿里百炼API配置
    dashscope_api_key: str = Field(..., env="DASHSCOPE_API_KEY") # pyright: ignore[reportCallIssue]
    dashscope_model: str = Field("paraformer-realtime-v1", env="DASHSCOPE_MODEL") # pyright: ignore[reportCallIssue]

    # FFmpeg配置
    ffmpeg_path: str = Field("", env="FFMPEG_PATH") # type: ignore
    ffprobe_path: str = Field("", env="FFPROBE_PATH") # type: ignore

    # 文件处理配置
    max_file_size: int = Field(10485760, env="MAX_FILE_SIZE")  # type: ignore # 10MB
    upload_dir: str = Field("./uploads", env="UPLOAD_DIR") # type: ignore
    temp_dir: str = Field("./temp", env="TEMP_DIR") # type: ignore

    # 语音识别配置
    default_language: str = Field("zh", env="DEFAULT_LANGUAGE") # type: ignore
    confidence_threshold: float = Field(0.5, env="CONFIDENCE_THRESHOLD") # type: ignore
    supported_formats: List[str] = Field(
        ["wav", "mp3", "m4a", "flac", "aac", "ogg", "webm", "amr", "opus", "wma"],
        env="SUPPORTED_FORMATS"
    ) # type: ignore

    # 日志配置
    log_level: str = Field("INFO", env="LOG_LEVEL") # type: ignore
    log_file: str = Field("./logs/ai_service.log", env="LOG_FILE") # type: ignore  # 基础路径，实际文件名为yyyyMMdd.log
    log_backup_count: int = Field(30, env="LOG_BACKUP_COUNT") # type: ignore  # 保留30天的日志文件

    # 安全配置
    cors_origins: List[str] = Field(
        ["*"],
        env="CORS_ORIGINS"
    ) # type: ignore
    api_key_header: str = Field("X-API-Key", env="API_KEY_HEADER") # type: ignore
    api_keys: List[str] = Field([], env="API_KEYS") # type: ignore

    # 性能配置
    worker_timeout: int = Field(30, env="WORKER_TIMEOUT") # type: ignore
    max_concurrent_requests: int = Field(10, env="MAX_CONCURRENT_REQUESTS") # type: ignore
    
    class Config:
        # 使用绝对路径确保能找到.env文件
        env_file = Path(__file__).parent.parent.parent / ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.upload_dir,
            self.temp_dir,
            os.path.dirname(self.log_file)
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    settings = Settings() # type: ignore
    settings.create_directories()
    return settings

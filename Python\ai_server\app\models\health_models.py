"""
健康检查相关数据模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from .common_models import BaseResponse


class HealthData(BaseModel):
    """健康检查数据"""
    status: str = Field(..., description="服务状态")
    timestamp: str = Field(..., description="检查时间")
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="版本号")
    system: Optional[Dict[str, Any]] = Field(default=None, description="系统信息")
    directories: Optional[Dict[str, Any]] = Field(default=None, description="目录信息")
    ffmpeg: Optional[Dict[str, Any]] = Field(default=None, description="FFmpeg信息")
    config: Optional[Dict[str, Any]] = Field(default=None, description="配置信息")
    issues: Optional[List[str]] = Field(default=None, description="问题列表")


class HealthResponse(BaseResponse):
    """健康检查响应"""
    data: Optional[HealthData] = Field(default=None, description="健康数据")


class ReadinessData(BaseModel):
    """就绪检查数据"""
    ready: bool = Field(..., description="是否就绪")
    timestamp: str = Field(..., description="检查时间")
    issues: List[str] = Field(default_factory=list, description="问题列表")


class ReadinessResponse(BaseResponse):
    """就绪检查响应"""
    data: Optional[ReadinessData] = Field(default=None, description="就绪数据")

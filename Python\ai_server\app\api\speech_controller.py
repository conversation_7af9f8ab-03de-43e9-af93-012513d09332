"""
语音识别控制器
"""

from typing import Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse

from ..services import SpeechService
from ..config import get_settings
from ..utils import get_logger
from ..models import (
    SpeechRecognitionResponse,
    ServiceStatusResponse,
    ModelsResponse,
    ErrorResponse
)

# 创建路由器
router = APIRouter(prefix="/speech", tags=["语音识别"])

# 创建兼容旧版本的路由器
compat_router = APIRouter(tags=["语音识别(兼容)"])

# 全局服务实例
speech_service = None
logger = get_logger(__name__)


def get_speech_service() -> SpeechService:
    """获取语音识别服务实例"""
    global speech_service
    if speech_service is None:
        speech_service = SpeechService()
    return speech_service


@router.post("/recognize", summary="语音识别", description="上传音频文件进行语音识别", response_model=SpeechRecognitionResponse)
async def recognize_speech(
    audio: UploadFile = File(..., description="音频文件"),
    service: SpeechService = Depends(get_speech_service)
) -> JSONResponse:
    """
    语音识别接口
    
    Args:
        audio: 上传的音频文件
        service: 语音识别服务
        
    Returns:
        识别结果
    """
    try:
        logger.info(f"接收语音识别请求: {audio.filename}")
        
        # 验证文件
        if not audio.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        if not audio.content_type or not audio.content_type.startswith('audio/'):
            logger.warning(f"可疑的文件类型: {audio.content_type}")
        
        # 执行语音识别
        result = await service.recognize_speech(audio)
        
        # 构建响应
        response_data = {
            "succeed": result.get("success", False),
            "data": {
                "text": result.get("text", ""),
                "confidence": result.get("confidence", 0.0),
                "duration": result.get("duration", 0.0),
                "language": result.get("language", "zh"),
                "model": result.get("model", ""),
                "file_info": result.get("file_info", {})
            },
            "messages": [result.get("error", "")] if result.get("error") else [],
            "timestamp": None  # 可以添加时间戳
        }
        
        logger.info(f"语音识别完成: {audio.filename} -> '{result.get('text', '')}'")
        return JSONResponse(content=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"语音识别失败: {e}")
        error_response = {
            "succeed": False,
            "data": None,
            "messages": [f"语音识别失败: {str(e)}"],
            "timestamp": None
        }
        return JSONResponse(content=error_response, status_code=500)


@router.get("/status", summary="服务状态", description="获取语音识别服务状态")
async def get_service_status(
    service: SpeechService = Depends(get_speech_service)
) -> JSONResponse:
    """
    获取服务状态
    
    Args:
        service: 语音识别服务
        
    Returns:
        服务状态信息
    """
    try:
        status = await service.get_service_status()
        
        response_data = {
            "succeed": True,
            "data": status,
            "messages": [],
            "timestamp": None
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"获取服务状态失败: {e}")
        error_response = {
            "succeed": False,
            "data": None,
            "messages": [f"获取服务状态失败: {str(e)}"],
            "timestamp": None
        }
        return JSONResponse(content=error_response, status_code=500)


@router.get("/models", summary="支持的模型", description="获取支持的语音识别模型列表")
async def get_supported_models() -> JSONResponse:
    """获取支持的模型列表"""
    try:
        settings = get_settings()
        
        models = [
            {
                "name": "paraformer-realtime-v1",
                "description": "实时Paraformer模型（推荐）",
                "type": "realtime",
                "current": settings.dashscope_model == "paraformer-realtime-v1"
            },
            {
                "name": "paraformer-v1", 
                "description": "标准Paraformer模型",
                "type": "standard",
                "current": settings.dashscope_model == "paraformer-v1"
            },
            {
                "name": "paraformer-8k-v1",
                "description": "8k采样率模型",
                "type": "low_sample_rate", 
                "current": settings.dashscope_model == "paraformer-8k-v1"
            },
            {
                "name": "paraformer-mtl-v1",
                "description": "多任务学习模型",
                "type": "multi_task",
                "current": settings.dashscope_model == "paraformer-mtl-v1"
            }
        ]
        
        response_data = {
            "succeed": True,
            "data": {
                "current_model": settings.dashscope_model,
                "models": models,
                "supported_formats": settings.supported_formats
            },
            "messages": [],
            "timestamp": None
        }
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        error_response = {
            "succeed": False,
            "data": None,
            "messages": [f"获取模型列表失败: {str(e)}"],
            "timestamp": None
        }
        return JSONResponse(content=error_response, status_code=500)


# 兼容旧版本的路由
@compat_router.post("/recognize", summary="语音识别(兼容)", description="兼容旧版本的语音识别接口")
async def recognize_speech_compat(
    audio: UploadFile = File(..., description="音频文件"),
    service: SpeechService = Depends(get_speech_service)
) -> JSONResponse:
    """兼容旧版本的语音识别接口"""
    return await recognize_speech(audio, service)


@compat_router.get("/GetServiceStatus", summary="服务状态(兼容)", description="兼容旧版本的服务状态接口")
async def get_service_status_compat(
    service: SpeechService = Depends(get_speech_service)
) -> JSONResponse:
    """兼容旧版本的服务状态接口"""
    return await get_service_status(service)

"""
通用数据模型
"""

from typing import Optional, Any, List
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""
    succeed: bool = Field(..., description="是否成功")
    messages: List[str] = Field(default_factory=list, description="消息列表")
    timestamp: Optional[str] = Field(default=None, description="时间戳")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    succeed: bool = Field(default=False, description="是否成功")
    data: Optional[Any] = Field(default=None, description="数据")
    messages: List[str] = Field(..., description="错误消息列表")
